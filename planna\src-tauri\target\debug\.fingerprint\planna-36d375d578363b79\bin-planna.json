{"rustc": 1842507548689473721, "features": "[]", "declared_features": "[]", "target": 10500583903386132459, "profile": 8731458305071235362, "path": 4942398508502643691, "deps": [[9689903380558560274, "serde", false, 4717576088014503742], [12852740929515239317, "planna_lib", false, 13052909344181233472], [12852740929515239317, "build_script_build", false, 1855981487288876452], [14039947826026167952, "tauri", false, 4896646188113380592], [15367738274754116744, "serde_json", false, 7090587761644971756], [16702348383442838006, "tauri_plugin_opener", false, 15729911311445530432]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\planna-36d375d578363b79\\dep-bin-planna", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}