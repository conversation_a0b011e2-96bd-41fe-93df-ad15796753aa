["\\\\?\\C:\\Data\\Projects\\Planna\\planna\\src-tauri\\target\\debug\\build\\tauri-8e35ce1df785c307\\out\\permissions\\menu\\autogenerated\\commands\\append.toml", "\\\\?\\C:\\Data\\Projects\\Planna\\planna\\src-tauri\\target\\debug\\build\\tauri-8e35ce1df785c307\\out\\permissions\\menu\\autogenerated\\commands\\create_default.toml", "\\\\?\\C:\\Data\\Projects\\Planna\\planna\\src-tauri\\target\\debug\\build\\tauri-8e35ce1df785c307\\out\\permissions\\menu\\autogenerated\\commands\\get.toml", "\\\\?\\C:\\Data\\Projects\\Planna\\planna\\src-tauri\\target\\debug\\build\\tauri-8e35ce1df785c307\\out\\permissions\\menu\\autogenerated\\commands\\insert.toml", "\\\\?\\C:\\Data\\Projects\\Planna\\planna\\src-tauri\\target\\debug\\build\\tauri-8e35ce1df785c307\\out\\permissions\\menu\\autogenerated\\commands\\is_checked.toml", "\\\\?\\C:\\Data\\Projects\\Planna\\planna\\src-tauri\\target\\debug\\build\\tauri-8e35ce1df785c307\\out\\permissions\\menu\\autogenerated\\commands\\is_enabled.toml", "\\\\?\\C:\\Data\\Projects\\Planna\\planna\\src-tauri\\target\\debug\\build\\tauri-8e35ce1df785c307\\out\\permissions\\menu\\autogenerated\\commands\\items.toml", "\\\\?\\C:\\Data\\Projects\\Planna\\planna\\src-tauri\\target\\debug\\build\\tauri-8e35ce1df785c307\\out\\permissions\\menu\\autogenerated\\commands\\new.toml", "\\\\?\\C:\\Data\\Projects\\Planna\\planna\\src-tauri\\target\\debug\\build\\tauri-8e35ce1df785c307\\out\\permissions\\menu\\autogenerated\\commands\\popup.toml", "\\\\?\\C:\\Data\\Projects\\Planna\\planna\\src-tauri\\target\\debug\\build\\tauri-8e35ce1df785c307\\out\\permissions\\menu\\autogenerated\\commands\\prepend.toml", "\\\\?\\C:\\Data\\Projects\\Planna\\planna\\src-tauri\\target\\debug\\build\\tauri-8e35ce1df785c307\\out\\permissions\\menu\\autogenerated\\commands\\remove.toml", "\\\\?\\C:\\Data\\Projects\\Planna\\planna\\src-tauri\\target\\debug\\build\\tauri-8e35ce1df785c307\\out\\permissions\\menu\\autogenerated\\commands\\remove_at.toml", "\\\\?\\C:\\Data\\Projects\\Planna\\planna\\src-tauri\\target\\debug\\build\\tauri-8e35ce1df785c307\\out\\permissions\\menu\\autogenerated\\commands\\set_accelerator.toml", "\\\\?\\C:\\Data\\Projects\\Planna\\planna\\src-tauri\\target\\debug\\build\\tauri-8e35ce1df785c307\\out\\permissions\\menu\\autogenerated\\commands\\set_as_app_menu.toml", "\\\\?\\C:\\Data\\Projects\\Planna\\planna\\src-tauri\\target\\debug\\build\\tauri-8e35ce1df785c307\\out\\permissions\\menu\\autogenerated\\commands\\set_as_help_menu_for_nsapp.toml", "\\\\?\\C:\\Data\\Projects\\Planna\\planna\\src-tauri\\target\\debug\\build\\tauri-8e35ce1df785c307\\out\\permissions\\menu\\autogenerated\\commands\\set_as_window_menu.toml", "\\\\?\\C:\\Data\\Projects\\Planna\\planna\\src-tauri\\target\\debug\\build\\tauri-8e35ce1df785c307\\out\\permissions\\menu\\autogenerated\\commands\\set_as_windows_menu_for_nsapp.toml", "\\\\?\\C:\\Data\\Projects\\Planna\\planna\\src-tauri\\target\\debug\\build\\tauri-8e35ce1df785c307\\out\\permissions\\menu\\autogenerated\\commands\\set_checked.toml", "\\\\?\\C:\\Data\\Projects\\Planna\\planna\\src-tauri\\target\\debug\\build\\tauri-8e35ce1df785c307\\out\\permissions\\menu\\autogenerated\\commands\\set_enabled.toml", "\\\\?\\C:\\Data\\Projects\\Planna\\planna\\src-tauri\\target\\debug\\build\\tauri-8e35ce1df785c307\\out\\permissions\\menu\\autogenerated\\commands\\set_icon.toml", "\\\\?\\C:\\Data\\Projects\\Planna\\planna\\src-tauri\\target\\debug\\build\\tauri-8e35ce1df785c307\\out\\permissions\\menu\\autogenerated\\commands\\set_text.toml", "\\\\?\\C:\\Data\\Projects\\Planna\\planna\\src-tauri\\target\\debug\\build\\tauri-8e35ce1df785c307\\out\\permissions\\menu\\autogenerated\\commands\\text.toml", "\\\\?\\C:\\Data\\Projects\\Planna\\planna\\src-tauri\\target\\debug\\build\\tauri-8e35ce1df785c307\\out\\permissions\\menu\\autogenerated\\default.toml"]