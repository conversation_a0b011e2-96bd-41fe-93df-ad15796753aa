{"rustc": 1842507548689473721, "features": "[\"brotli\", \"compression\"]", "declared_features": "[\"brotli\", \"compression\", \"config-json5\", \"config-toml\", \"isolation\"]", "target": 17460618180909919773, "profile": 2225463790103693989, "path": 5973045599654045360, "deps": [[2671782512663819132, "tauri_utils", false, 9997456220751863589], [3060637413840920116, "proc_macro2", false, 10692526903629513639], [3150220818285335163, "url", false, 2733448453184830458], [4899080583175475170, "semver", false, 1510945248306732154], [4974441333307933176, "syn", false, 18272012117846485449], [7170110829644101142, "json_patch", false, 11543372679734088708], [7392050791754369441, "ico", false, 6106535312969787205], [8319709847752024821, "uuid", false, 8486328388127030308], [9556762810601084293, "brotli", false, 4648191850979607455], [9689903380558560274, "serde", false, 14259188998479785018], [9857275760291862238, "sha2", false, 17885868543714371611], [10806645703491011684, "thiserror", false, 16626167402001732720], [12687914511023397207, "png", false, 5110661303750108344], [13077212702700853852, "base64", false, 13747060264500561464], [15367738274754116744, "serde_json", false, 5877451486395265118], [15622660310229662834, "walkdir", false, 12728780776187979975], [17990358020177143287, "quote", false, 17136168538474977059]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-codegen-2d7667fbc042d00d\\dep-lib-tauri_codegen", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}