{"rustc": 1842507548689473721, "features": "[\"compression\"]", "declared_features": "[\"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"isolation\", \"tracing\"]", "target": 4649449654257170297, "profile": 2225463790103693989, "path": 5452798990686781434, "deps": [[2671782512663819132, "tauri_utils", false, 9997456220751863589], [3060637413840920116, "proc_macro2", false, 10692526903629513639], [4974441333307933176, "syn", false, 18272012117846485449], [13077543566650298139, "heck", false, 9773368077212947622], [14455244907590647360, "tauri_codegen", false, 2386122358811928248], [17990358020177143287, "quote", false, 17136168538474977059]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-macros-2184f39a83d8c890\\dep-lib-tauri_macros", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}