{"rustc": 1842507548689473721, "features": "[\"common-controls-v6\", \"x11\"]", "declared_features": "[\"common-controls-v6\", \"default\", \"devtools\", \"macos-private-api\", \"macos-proxy\", \"objc-exception\", \"tracing\", \"unstable\", \"x11\"]", "target": 1901661049345253480, "profile": 15657897354478470176, "path": 14452642017043860950, "deps": [[376837177317575824, "softbuffer", false, 8353652082660449207], [2013030631243296465, "webview2_com", false, 15691380690616046722], [2671782512663819132, "tauri_utils", false, 8701964448413515733], [3150220818285335163, "url", false, 4293664981472059862], [3722963349756955755, "once_cell", false, 8702761537868055165], [4143744114649553716, "raw_window_handle", false, 8369169895596814157], [5986029879202738730, "log", false, 14185100642484028674], [6089812615193535349, "tauri_runtime", false, 9717992195511244738], [8826339825490770380, "tao", false, 1777288853954052531], [9010263965687315507, "http", false, 14658643743866572374], [9141053277961803901, "wry", false, 13242055855639742945], [11599800339996261026, "build_script_build", false, 11022286605824089311], [14585479307175734061, "windows", false, 8482151277868524196]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-wry-36607f32bda88c57\\dep-lib-tauri_runtime_wry", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}