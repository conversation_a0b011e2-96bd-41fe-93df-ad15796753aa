import React from "react";

export function UnitEconomicsPanel() {
  // Sample data - this will be replaced with real calculations
  const currentMonth = "Feb 2026";
  const metrics = {
    totalCustomers: 578,
    newThisMonth: 164,
    blendedARPU: 83,
    mrr: 48061,
    arr: 576732,
    salesTeamCost: 15000,
    marketingCost: 12000,
    eventsThisMonth: 5000,
    totalCAC: 195,
    avgCustomerLife: 30,
    ltv: 2490,
    ltvCacRatio: 12.8,
    paybackPeriod: 2.3,
    cashOnHand: 89425,
    monthlyBurn: 32000,
    runway: 2.8,
    breakEven: "May 2026",
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value);
  };

  const formatNumber = (value: number) => {
    return new Intl.NumberFormat("en-US").format(value);
  };

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="px-6 py-4 border-b border-slate-200">
        <h2 className="text-lg font-semibold text-slate-900">Unit Economics</h2>
        <p className="text-sm text-slate-600">{currentMonth}</p>
      </div>

      <div className="flex-1 overflow-y-auto p-6 space-y-6">
        {/* Customer Metrics */}
        <div>
          <h3 className="text-sm font-semibold text-slate-900 mb-3 pb-2 border-b border-slate-200">Customer Metrics</h3>
          <div className="space-y-2">
            <div className="flex justify-between items-center">
              <span className="text-sm text-slate-600">Total Customers</span>
              <span className="text-sm font-semibold text-slate-900">{formatNumber(metrics.totalCustomers)}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-slate-600">New This Month</span>
              <span className="text-sm font-semibold text-green-600">{formatNumber(metrics.newThisMonth)}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-slate-600">Blended ARPU</span>
              <span className="text-sm font-semibold text-slate-900">{formatCurrency(metrics.blendedARPU)}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-slate-600">MRR</span>
              <span className="text-sm font-semibold text-blue-600">{formatCurrency(metrics.mrr)}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-slate-600">ARR</span>
              <span className="text-sm font-semibold text-blue-600">{formatCurrency(metrics.arr)}</span>
            </div>
          </div>
        </div>

        {/* Acquisition Costs */}
        <div>
          <h3 className="text-sm font-semibold text-slate-900 mb-3 pb-2 border-b border-slate-200">Acquisition Costs</h3>
          <div className="space-y-2">
            <div className="flex justify-between items-center">
              <span className="text-sm text-slate-600">Sales Team Cost</span>
              <span className="text-sm font-semibold text-slate-900">{formatCurrency(metrics.salesTeamCost)}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-slate-600">Marketing Cost</span>
              <span className="text-sm font-semibold text-slate-900">{formatCurrency(metrics.marketingCost)}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-slate-600">Events This Month</span>
              <span className="text-sm font-semibold text-slate-900">{formatCurrency(metrics.eventsThisMonth)}</span>
            </div>
            <div className="border-t border-slate-200 pt-2 mt-2">
              <div className="flex justify-between items-center">
                <span className="text-sm font-semibold text-slate-900">Total CAC</span>
                <span className="text-sm font-bold text-red-600">{formatCurrency(metrics.totalCAC)}</span>
              </div>
            </div>
          </div>
        </div>

        {/* Lifetime Value */}
        <div>
          <h3 className="text-sm font-semibold text-slate-900 mb-3 pb-2 border-b border-slate-200">Lifetime Value</h3>
          <div className="space-y-2">
            <div className="flex justify-between items-center">
              <span className="text-sm text-slate-600">Avg Customer Life</span>
              <span className="text-sm font-semibold text-slate-900">{metrics.avgCustomerLife}mo</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-slate-600">LTV</span>
              <span className="text-sm font-semibold text-green-600">{formatCurrency(metrics.ltv)}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-slate-600">LTV:CAC Ratio</span>
              <span className={`text-sm font-semibold ${metrics.ltvCacRatio >= 3 ? 'text-green-600' : 'text-red-600'}`}>
                {metrics.ltvCacRatio}:1
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-slate-600">Payback Period</span>
              <span className="text-sm font-semibold text-slate-900">{metrics.paybackPeriod}mo</span>
            </div>
          </div>
        </div>

        {/* Financial Health */}
        <div>
          <h3 className="text-sm font-semibold text-slate-900 mb-3 pb-2 border-b border-slate-200">Financial Health</h3>
          <div className="space-y-2">
            <div className="flex justify-between items-center">
              <span className="text-sm text-slate-600">Cash on Hand</span>
              <span className="text-sm font-semibold text-slate-900">{formatCurrency(metrics.cashOnHand)}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-slate-600">Monthly Burn</span>
              <span className="text-sm font-semibold text-red-600">{formatCurrency(metrics.monthlyBurn)}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-slate-600">Runway</span>
              <span className={`text-sm font-semibold ${metrics.runway < 3 ? 'text-red-600' : metrics.runway < 6 ? 'text-yellow-600' : 'text-green-600'}`}>
                {metrics.runway} months
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-slate-600">Break-even</span>
              <span className="text-sm font-semibold text-green-600">{metrics.breakEven}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
