{"rustc": 1842507548689473721, "features": "[\"common-controls-v6\", \"compression\", \"default\", \"dynamic-acl\", \"tauri-runtime-wry\", \"webkit2gtk\", \"webview2-com\", \"wry\", \"x11\"]", "declared_features": "[\"common-controls-v6\", \"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"data-url\", \"default\", \"devtools\", \"dynamic-acl\", \"http-range\", \"image\", \"image-ico\", \"image-png\", \"isolation\", \"linux-libxdo\", \"macos-private-api\", \"macos-proxy\", \"native-tls\", \"native-tls-vendored\", \"objc-exception\", \"process-relaunch-dangerous-allow-symlink-macos\", \"protocol-asset\", \"rustls-tls\", \"specta\", \"tauri-runtime-wry\", \"test\", \"tracing\", \"tray-icon\", \"unstable\", \"uuid\", \"webkit2gtk\", \"webview-data-url\", \"webview2-com\", \"wry\", \"x11\"]", "target": 12223948975794516716, "profile": 15657897354478470176, "path": 4886512553799680475, "deps": [[40386456601120721, "percent_encoding", false, 1962975240131137444], [1200537532907108615, "url<PERSON><PERSON>n", false, 3500415788311623526], [2013030631243296465, "webview2_com", false, 15691380690616046722], [2671782512663819132, "tauri_utils", false, 8701964448413515733], [3150220818285335163, "url", false, 4293664981472059862], [3331586631144870129, "getrandom", false, 11131266246066710524], [4143744114649553716, "raw_window_handle", false, 8369169895596814157], [4494683389616423722, "muda", false, 6391561659455276007], [4919829919303820331, "serialize_to_javascript", false, 16876103998965461877], [5986029879202738730, "log", false, 14185100642484028674], [6089812615193535349, "tauri_runtime", false, 9717992195511244738], [7573826311589115053, "tauri_macros", false, 16608936467976463880], [9010263965687315507, "http", false, 14658643743866572374], [9689903380558560274, "serde", false, 4717576088014503742], [10229185211513642314, "mime", false, 14727713614198683120], [10806645703491011684, "thiserror", false, 16626167402001732720], [11599800339996261026, "tauri_runtime_wry", false, 10243374256997102283], [11989259058781683633, "dunce", false, 9288129419543260968], [12393800526703971956, "tokio", false, 18103572403642606186], [12565293087094287914, "window_vibrancy", false, 10676089386723051159], [12986574360607194341, "serde_repr", false, 2950631275343138971], [13077543566650298139, "heck", false, 9773368077212947622], [13625485746686963219, "anyhow", false, 15029994100314801627], [14039947826026167952, "build_script_build", false, 15433919193513939152], [14585479307175734061, "windows", false, 8482151277868524196], [15367738274754116744, "serde_json", false, 7090587761644971756], [16928111194414003569, "dirs", false, 15306885009778154891], [17155886227862585100, "glob", false, 16602312495278986287]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-daea92f3c01bba27\\dep-lib-tauri", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}