{"rustc": 1842507548689473721, "features": "[\"drag-drop\", \"gdkx11\", \"javascriptcore-rs\", \"linux-body\", \"os-webview\", \"protocol\", \"soup3\", \"webkit2gtk\", \"webkit2gtk-sys\", \"x11\", \"x11-dl\"]", "declared_features": "[\"default\", \"devtools\", \"drag-drop\", \"fullscreen\", \"gdkx11\", \"javascriptcore-rs\", \"linux-body\", \"mac-proxy\", \"os-webview\", \"protocol\", \"serde\", \"soup3\", \"tracing\", \"transparent\", \"webkit2gtk\", \"webkit2gtk-sys\", \"x11\", \"x11-dl\"]", "target": 2463569863749872413, "profile": 11987593577772629573, "path": 15528644545335285249, "deps": [[2013030631243296465, "webview2_com", false, 15691380690616046722], [3334271191048661305, "windows_version", false, 12410096159256910158], [3722963349756955755, "once_cell", false, 8702761537868055165], [4143744114649553716, "raw_window_handle", false, 8369169895596814157], [5628259161083531273, "windows_core", false, 2278988278485010916], [7606335748176206944, "dpi", false, 18186376647837088705], [9010263965687315507, "http", false, 14658643743866572374], [9141053277961803901, "build_script_build", false, 18076433568717343345], [10806645703491011684, "thiserror", false, 16626167402001732720], [11989259058781683633, "dunce", false, 9288129419543260968], [14585479307175734061, "windows", false, 8482151277868524196], [16727543399706004146, "cookie", false, 11327141274010568822]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\wry-8c07836d04e9e0ae\\dep-lib-wry", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}