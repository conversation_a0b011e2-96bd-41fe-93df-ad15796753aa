import React from "react";
import { BenchmarkPanel } from "./components/BenchmarkPanel";
import { ChartArea } from "./components/ChartArea";
import { UnitEconomicsPanel } from "./components/UnitEconomicsPanel";
import { MonthlyInputTable } from "./components/MonthlyInputTable";

function App() {
  return (
    <div className="h-screen flex flex-col bg-slate-50">
      {/* Header */}
      <div className="bg-white border-b border-slate-200 px-6 py-3 flex-shrink-0">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-xl font-bold text-slate-900">Planna</h1>
            <p className="text-xs text-slate-600">Growth Planning Tool</p>
          </div>
          <div className="flex items-center gap-3">
            <button className="px-3 py-1.5 text-sm font-medium text-slate-600 hover:text-slate-900 transition-colors">
              Export
            </button>
            <button className="px-3 py-1.5 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700 transition-colors">
              Save
            </button>
          </div>
        </div>
      </div>

      {/* Benchmark Variables Panel - Collapsible top section */}
      <div className="bg-white border-b border-slate-200 flex-shrink-0">
        <BenchmarkPanel />
      </div>

      {/* Main Content Area - Chart + Unit Economics */}
      <div className="flex flex-1 min-h-0">
        {/* Chart Area - Takes up most space */}
        <div className="flex-1 bg-white border-r border-slate-200">
          <ChartArea />
        </div>

        {/* Unit Economics Panel - Right sidebar */}
        <div className="w-80 bg-slate-50 border-r border-slate-200">
          <UnitEconomicsPanel />
        </div>
      </div>

      {/* Monthly Input Table - Bottom section */}
      <div className="bg-white border-t border-slate-200 flex-shrink-0 h-64">
        <MonthlyInputTable />
      </div>
    </div>
  );
}

export default App;
