{"rustc": 1842507548689473721, "features": "[\"brotli\", \"build\", \"cargo_metadata\", \"compression\", \"html-manipulation\", \"proc-macro2\", \"quote\", \"resources\", \"schema\", \"schemars\", \"swift-rs\", \"walkdir\"]", "declared_features": "[\"aes-gcm\", \"brotli\", \"build\", \"cargo_metadata\", \"compression\", \"config-json5\", \"config-toml\", \"getrandom\", \"html-manipulation\", \"isolation\", \"json5\", \"proc-macro2\", \"process-relaunch-dangerous-allow-symlink-macos\", \"quote\", \"resources\", \"schema\", \"schemars\", \"serialize-to-javascript\", \"swift-rs\", \"walkdir\"]", "target": 7530130812022395703, "profile": 2225463790103693989, "path": 12372222847082620782, "deps": [[1200537532907108615, "url<PERSON><PERSON>n", false, 6485510369339560562], [3060637413840920116, "proc_macro2", false, 10692526903629513639], [3150220818285335163, "url", false, 2733448453184830458], [3191507132440681679, "serde_untagged", false, 8688333724865142038], [4071963112282141418, "serde_with", false, 15398719305335488026], [4899080583175475170, "semver", false, 1510945248306732154], [5986029879202738730, "log", false, 14185100642484028674], [6606131838865521726, "ctor", false, 7004454792262414865], [6913375703034175521, "schemars", false, 17008654131786635838], [7170110829644101142, "json_patch", false, 11543372679734088708], [8319709847752024821, "uuid", false, 8486328388127030308], [9010263965687315507, "http", false, 14658643743866572374], [9451456094439810778, "regex", false, 14684964806606779790], [9556762810601084293, "brotli", false, 4648191850979607455], [9689903380558560274, "serde", false, 14259188998479785018], [10806645703491011684, "thiserror", false, 16626167402001732720], [11655476559277113544, "cargo_metadata", false, 8661649207450939006], [11989259058781683633, "dunce", false, 9288129419543260968], [13625485746686963219, "anyhow", false, 15029994100314801627], [14232843520438415263, "html5ever", false, 10998845190447935053], [15088007382495681292, "kuchiki", false, 10033914330232950074], [15367738274754116744, "serde_json", false, 5877451486395265118], [15609422047640926750, "toml", false, 10475929893644939239], [15622660310229662834, "walkdir", false, 12728780776187979975], [15932120279885307830, "memchr", false, 9615746733239688948], [17146114186171651583, "infer", false, 10452757752683712664], [17155886227862585100, "glob", false, 16602312495278986287], [17186037756130803222, "phf", false, 3850925010826600019], [17990358020177143287, "quote", false, 17136168538474977059]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-utils-68904c5ebfd10cd9\\dep-lib-tauri_utils", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}