{"rustc": 1842507548689473721, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[12852740929515239317, "build_script_build", false, 4317817977947466654], [14039947826026167952, "build_script_build", false, 15433919193513939152], [16702348383442838006, "build_script_build", false, 6115534819034729258]], "local": [{"RerunIfChanged": {"output": "debug\\build\\planna-76be83ffe176eda6\\output", "paths": ["tauri.conf.json", "capabilities"]}}, {"RerunIfEnvChanged": {"var": "TAURI_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}