{"rustc": 1842507548689473721, "features": "[\"brotli\", \"compression\", \"resources\", \"walkdir\"]", "declared_features": "[\"aes-gcm\", \"brotli\", \"build\", \"cargo_metadata\", \"compression\", \"config-json5\", \"config-toml\", \"getrandom\", \"html-manipulation\", \"isolation\", \"json5\", \"proc-macro2\", \"process-relaunch-dangerous-allow-symlink-macos\", \"quote\", \"resources\", \"schema\", \"schemars\", \"serialize-to-javascript\", \"swift-rs\", \"walkdir\"]", "target": 7530130812022395703, "profile": 15657897354478470176, "path": 12372222847082620782, "deps": [[1200537532907108615, "url<PERSON><PERSON>n", false, 3500415788311623526], [3150220818285335163, "url", false, 4293664981472059862], [3191507132440681679, "serde_untagged", false, 2911946121761747842], [4071963112282141418, "serde_with", false, 9115721637244944030], [4899080583175475170, "semver", false, 4600793094972569096], [5986029879202738730, "log", false, 14185100642484028674], [6606131838865521726, "ctor", false, 7004454792262414865], [7170110829644101142, "json_patch", false, 18189260624037411775], [8319709847752024821, "uuid", false, 14246635556169586303], [9010263965687315507, "http", false, 14658643743866572374], [9451456094439810778, "regex", false, 14684964806606779790], [9556762810601084293, "brotli", false, 4648191850979607455], [9689903380558560274, "serde", false, 4717576088014503742], [10806645703491011684, "thiserror", false, 16626167402001732720], [11989259058781683633, "dunce", false, 9288129419543260968], [13625485746686963219, "anyhow", false, 15029994100314801627], [15367738274754116744, "serde_json", false, 7090587761644971756], [15609422047640926750, "toml", false, 12297383860789565355], [15622660310229662834, "walkdir", false, 26477700949258191], [15932120279885307830, "memchr", false, 9615746733239688948], [17146114186171651583, "infer", false, 1649973784482995899], [17155886227862585100, "glob", false, 16602312495278986287], [17186037756130803222, "phf", false, 8042188339177548266]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-utils-eea912f63ce6eea5\\dep-lib-tauri_utils", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}