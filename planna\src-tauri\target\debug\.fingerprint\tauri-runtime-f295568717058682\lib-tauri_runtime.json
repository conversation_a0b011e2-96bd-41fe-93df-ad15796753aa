{"rustc": 1842507548689473721, "features": "[]", "declared_features": "[\"devtools\", \"macos-private-api\"]", "target": 10306386172444932100, "profile": 15657897354478470176, "path": 8554150275918461623, "deps": [[2671782512663819132, "tauri_utils", false, 8701964448413515733], [3150220818285335163, "url", false, 4293664981472059862], [4143744114649553716, "raw_window_handle", false, 8369169895596814157], [6089812615193535349, "build_script_build", false, 13944386230946494574], [7606335748176206944, "dpi", false, 18186376647837088705], [9010263965687315507, "http", false, 14658643743866572374], [9689903380558560274, "serde", false, 4717576088014503742], [10806645703491011684, "thiserror", false, 16626167402001732720], [14585479307175734061, "windows", false, 8482151277868524196], [15367738274754116744, "serde_json", false, 7090587761644971756], [16727543399706004146, "cookie", false, 11327141274010568822]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-runtime-f295568717058682\\dep-lib-tauri_runtime", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}