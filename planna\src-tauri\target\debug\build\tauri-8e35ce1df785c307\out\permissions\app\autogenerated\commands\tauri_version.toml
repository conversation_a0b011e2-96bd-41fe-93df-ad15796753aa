# Copyright 2019-2024 Tauri Programme within The Commons Conservancy
# SPDX-License-Identifier: Apache-2.0
# SPDX-License-Identifier: MIT
# Automatically generated - DO NOT EDIT!

[[permission]]
identifier = "allow-tauri-version"
description = "Enables the tauri_version command without any pre-configured scope."
commands.allow = ["tauri_version"]

[[permission]]
identifier = "deny-tauri-version"
description = "Denies the tauri_version command without any pre-configured scope."
commands.deny = ["tauri_version"]
