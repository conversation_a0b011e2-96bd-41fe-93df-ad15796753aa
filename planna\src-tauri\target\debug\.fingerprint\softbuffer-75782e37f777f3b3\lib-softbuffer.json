{"rustc": 1842507548689473721, "features": "[]", "declared_features": "[\"as-raw-xcb-connection\", \"bytemuck\", \"default\", \"drm\", \"fastrand\", \"kms\", \"memmap2\", \"rustix\", \"tiny-xlib\", \"wayland\", \"wayland-backend\", \"wayland-client\", \"wayland-dlopen\", \"wayland-sys\", \"x11\", \"x11-dlopen\", \"x11rb\"]", "target": 9174284484934603102, "profile": 15657897354478470176, "path": 1562132350472439588, "deps": [[376837177317575824, "build_script_build", false, 12923348684747572741], [4143744114649553716, "raw_window_handle", false, 8369169895596814157], [5986029879202738730, "log", false, 14185100642484028674], [10281541584571964250, "windows_sys", false, 5778605177054635130]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\softbuffer-75782e37f777f3b3\\dep-lib-softbuffer", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}