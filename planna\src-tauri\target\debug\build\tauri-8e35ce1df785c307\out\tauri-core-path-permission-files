["\\\\?\\C:\\Data\\Projects\\Planna\\planna\\src-tauri\\target\\debug\\build\\tauri-8e35ce1df785c307\\out\\permissions\\path\\autogenerated\\commands\\basename.toml", "\\\\?\\C:\\Data\\Projects\\Planna\\planna\\src-tauri\\target\\debug\\build\\tauri-8e35ce1df785c307\\out\\permissions\\path\\autogenerated\\commands\\dirname.toml", "\\\\?\\C:\\Data\\Projects\\Planna\\planna\\src-tauri\\target\\debug\\build\\tauri-8e35ce1df785c307\\out\\permissions\\path\\autogenerated\\commands\\extname.toml", "\\\\?\\C:\\Data\\Projects\\Planna\\planna\\src-tauri\\target\\debug\\build\\tauri-8e35ce1df785c307\\out\\permissions\\path\\autogenerated\\commands\\is_absolute.toml", "\\\\?\\C:\\Data\\Projects\\Planna\\planna\\src-tauri\\target\\debug\\build\\tauri-8e35ce1df785c307\\out\\permissions\\path\\autogenerated\\commands\\join.toml", "\\\\?\\C:\\Data\\Projects\\Planna\\planna\\src-tauri\\target\\debug\\build\\tauri-8e35ce1df785c307\\out\\permissions\\path\\autogenerated\\commands\\normalize.toml", "\\\\?\\C:\\Data\\Projects\\Planna\\planna\\src-tauri\\target\\debug\\build\\tauri-8e35ce1df785c307\\out\\permissions\\path\\autogenerated\\commands\\resolve.toml", "\\\\?\\C:\\Data\\Projects\\Planna\\planna\\src-tauri\\target\\debug\\build\\tauri-8e35ce1df785c307\\out\\permissions\\path\\autogenerated\\commands\\resolve_directory.toml", "\\\\?\\C:\\Data\\Projects\\Planna\\planna\\src-tauri\\target\\debug\\build\\tauri-8e35ce1df785c307\\out\\permissions\\path\\autogenerated\\default.toml"]