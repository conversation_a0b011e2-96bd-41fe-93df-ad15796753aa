["\\\\?\\C:\\Data\\Projects\\Planna\\planna\\src-tauri\\target\\debug\\build\\tauri-8e35ce1df785c307\\out\\permissions\\tray\\autogenerated\\commands\\get_by_id.toml", "\\\\?\\C:\\Data\\Projects\\Planna\\planna\\src-tauri\\target\\debug\\build\\tauri-8e35ce1df785c307\\out\\permissions\\tray\\autogenerated\\commands\\new.toml", "\\\\?\\C:\\Data\\Projects\\Planna\\planna\\src-tauri\\target\\debug\\build\\tauri-8e35ce1df785c307\\out\\permissions\\tray\\autogenerated\\commands\\remove_by_id.toml", "\\\\?\\C:\\Data\\Projects\\Planna\\planna\\src-tauri\\target\\debug\\build\\tauri-8e35ce1df785c307\\out\\permissions\\tray\\autogenerated\\commands\\set_icon.toml", "\\\\?\\C:\\Data\\Projects\\Planna\\planna\\src-tauri\\target\\debug\\build\\tauri-8e35ce1df785c307\\out\\permissions\\tray\\autogenerated\\commands\\set_icon_as_template.toml", "\\\\?\\C:\\Data\\Projects\\Planna\\planna\\src-tauri\\target\\debug\\build\\tauri-8e35ce1df785c307\\out\\permissions\\tray\\autogenerated\\commands\\set_menu.toml", "\\\\?\\C:\\Data\\Projects\\Planna\\planna\\src-tauri\\target\\debug\\build\\tauri-8e35ce1df785c307\\out\\permissions\\tray\\autogenerated\\commands\\set_show_menu_on_left_click.toml", "\\\\?\\C:\\Data\\Projects\\Planna\\planna\\src-tauri\\target\\debug\\build\\tauri-8e35ce1df785c307\\out\\permissions\\tray\\autogenerated\\commands\\set_temp_dir_path.toml", "\\\\?\\C:\\Data\\Projects\\Planna\\planna\\src-tauri\\target\\debug\\build\\tauri-8e35ce1df785c307\\out\\permissions\\tray\\autogenerated\\commands\\set_title.toml", "\\\\?\\C:\\Data\\Projects\\Planna\\planna\\src-tauri\\target\\debug\\build\\tauri-8e35ce1df785c307\\out\\permissions\\tray\\autogenerated\\commands\\set_tooltip.toml", "\\\\?\\C:\\Data\\Projects\\Planna\\planna\\src-tauri\\target\\debug\\build\\tauri-8e35ce1df785c307\\out\\permissions\\tray\\autogenerated\\commands\\set_visible.toml", "\\\\?\\C:\\Data\\Projects\\Planna\\planna\\src-tauri\\target\\debug\\build\\tauri-8e35ce1df785c307\\out\\permissions\\tray\\autogenerated\\default.toml"]