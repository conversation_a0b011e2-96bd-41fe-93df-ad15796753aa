import React, { useState } from "react";

interface MonthData {
  month: string;
  salesReps: number;
  marketingTeam: number;
  devTeam: number;
  opsTeam: number;
  leadershipTeam: number;
  eventsCost: number;
  totalBurn: number;
}

const initialData: MonthData[] = [
  { month: "Aug '25", salesReps: 1, marketingTeam: 0, devTeam: 0, opsTeam: 0, leadershipTeam: 0, eventsCost: 2000, totalBurn: 5000 },
  { month: "Sep '25", salesReps: 2, marketingTeam: 1, devTeam: 0, opsTeam: 0, leadershipTeam: 0, eventsCost: 3500, totalBurn: 11500 },
  { month: "Oct '25", salesReps: 3, marketingTeam: 1, devTeam: 0, opsTeam: 0, leadershipTeam: 0, eventsCost: 2500, totalBurn: 14500 },
  { month: "Nov '25", salesReps: 4, marketingTeam: 2, devTeam: 0, opsTeam: 0, leadershipTeam: 0, eventsCost: 5000, totalBurn: 23000 },
  { month: "Dec '25", salesReps: 5, marketingTeam: 3, devTeam: 0, opsTeam: 0, leadershipTeam: 0, eventsCost: 8000, totalBurn: 32000 },
  { month: "Jan '26", salesReps: 5, marketingTeam: 4, devTeam: 0, opsTeam: 1, leadershipTeam: 0, eventsCost: 10000, totalBurn: 40000 },
  { month: "Feb '26", salesReps: 5, marketingTeam: 4, devTeam: 0, opsTeam: 1, leadershipTeam: 0, eventsCost: 5000, totalBurn: 35000 },
];

export function MonthlyInputTable() {
  const [data, setData] = useState<MonthData[]>(initialData);

  const updateCell = (rowIndex: number, field: keyof MonthData, value: string) => {
    const newData = [...data];
    const numValue = field === 'month' ? value : parseInt(value) || 0;
    newData[rowIndex] = { ...newData[rowIndex], [field]: numValue };

    // Recalculate total burn (simplified calculation)
    if (field !== 'month' && field !== 'totalBurn') {
      const row = newData[rowIndex];
      const teamCost = (row.salesReps * 3000) + (row.marketingTeam * 3000) +
                      (row.devTeam * 4000) + (row.opsTeam * 3000) +
                      (row.leadershipTeam * 5000);
      newData[rowIndex].totalBurn = teamCost + row.eventsCost + 2000; // +2000 for fixed costs
    }

    setData(newData);
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value);
  };

  const columns = [
    { key: "month", label: "Month", width: "w-24" },
    { key: "salesReps", label: "Sales", width: "w-16" },
    { key: "marketingTeam", label: "Marketing", width: "w-20" },
    { key: "devTeam", label: "Dev", width: "w-16" },
    { key: "opsTeam", label: "Ops", width: "w-16" },
    { key: "leadershipTeam", label: "Leadership", width: "w-20" },
    { key: "eventsCost", label: "Events", width: "w-20" },
    { key: "totalBurn", label: "Total Burn", width: "w-24" },
  ];

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="px-6 py-4 border-b border-slate-200">
        <h2 className="text-lg font-semibold text-slate-900">Month-by-Month Planning</h2>
        <p className="text-sm text-slate-600">Plan your team growth and expenses over time</p>
      </div>

      {/* Table */}
      <div className="flex-1 overflow-auto">
        <table className="w-full">
          <thead className="bg-slate-50 border-b border-slate-200 sticky top-0">
            <tr>
              {columns.map((column) => (
                <th
                  key={column.key}
                  className="px-4 py-3 text-left text-xs font-semibold text-slate-900 uppercase tracking-wider"
                >
                  {column.label}
                </th>
              ))}
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-slate-200">
            {data.map((row, rowIndex) => (
              <tr key={row.month} className="hover:bg-slate-50">
                {columns.map((column) => (
                  <td key={column.key} className="px-4 py-3">
                    {column.key === 'month' ? (
                      <span className="text-sm font-medium text-slate-900">{row[column.key]}</span>
                    ) : column.key === 'totalBurn' ? (
                      <span className={`text-sm font-semibold ${
                        row.totalBurn > 50000 ? 'text-red-600' :
                        row.totalBurn > 30000 ? 'text-yellow-600' :
                        'text-green-600'
                      }`}>
                        {formatCurrency(row.totalBurn)}
                      </span>
                    ) : column.key === 'eventsCost' ? (
                      <input
                        type="number"
                        value={row[column.key]}
                        onChange={(e) => updateCell(rowIndex, column.key as keyof MonthData, e.target.value)}
                        className="w-20 px-2 py-1 text-sm border border-slate-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        min="0"
                      />
                    ) : (
                      <input
                        type="number"
                        value={row[column.key as keyof MonthData]}
                        onChange={(e) => updateCell(rowIndex, column.key as keyof MonthData, e.target.value)}
                        className="w-16 px-2 py-1 text-sm border border-slate-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-center"
                        min="0"
                      />
                    )}
                  </td>
                ))}
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
}
