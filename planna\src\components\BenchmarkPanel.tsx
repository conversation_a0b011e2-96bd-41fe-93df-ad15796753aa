import React, { useState } from "react";

export function BenchmarkPanel() {
  const [isExpanded, setIsExpanded] = useState(false);

  return (
    <div className="px-6 py-4">
      <div className="flex items-center justify-between mb-4">
        <div>
          <h2 className="text-lg font-semibold text-slate-900">Benchmark Variables</h2>
          <p className="text-sm text-slate-600">Configure your business assumptions</p>
        </div>
        <button
          onClick={() => setIsExpanded(!isExpanded)}
          className="px-3 py-1.5 text-sm font-medium text-slate-600 hover:text-slate-900 border border-slate-300 rounded-md hover:bg-slate-50 transition-colors"
        >
          {isExpanded ? 'Collapse' : 'Expand'}
        </button>
      </div>

      {isExpanded && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {/* Revenue Assumptions */}
          <div className="space-y-4">
            <h3 className="font-medium text-slate-900 border-b border-slate-200 pb-2">Revenue Assumptions</h3>

            <div>
              <h4 className="text-sm font-medium text-slate-700 mb-3">Pricing Tiers</h4>
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <label className="text-sm text-slate-600 w-20">Basic:</label>
                  <input
                    type="number"
                    defaultValue={50}
                    className="flex-1 px-2 py-1 text-sm border border-slate-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                  <span className="text-sm text-slate-500">/mo</span>
                </div>
                <div className="flex items-center gap-2">
                  <label className="text-sm text-slate-600 w-20">Standard:</label>
                  <input
                    type="number"
                    defaultValue={99}
                    className="flex-1 px-2 py-1 text-sm border border-slate-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                  <span className="text-sm text-slate-500">/mo</span>
                </div>
                <div className="flex items-center gap-2">
                  <label className="text-sm text-slate-600 w-20">Pro:</label>
                  <input
                    type="number"
                    defaultValue={199}
                    className="flex-1 px-2 py-1 text-sm border border-slate-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                  <span className="text-sm text-slate-500">/mo</span>
                </div>
                <div className="flex items-center gap-2">
                  <label className="text-sm text-slate-600 w-20">Enterprise:</label>
                  <input
                    type="number"
                    defaultValue={400}
                    className="flex-1 px-2 py-1 text-sm border border-slate-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                  <span className="text-sm text-slate-500">/mo</span>
                </div>
              </div>
            </div>

            <div>
              <h4 className="text-sm font-medium text-slate-700 mb-3">Customer Mix</h4>
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <label className="text-sm text-slate-600 w-20">Basic:</label>
                  <input
                    type="number"
                    defaultValue={49}
                    max={100}
                    min={0}
                    className="flex-1 px-2 py-1 text-sm border border-slate-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                  <span className="text-sm text-slate-500">%</span>
                </div>
                <div className="flex items-center gap-2">
                  <label className="text-sm text-slate-600 w-20">Standard:</label>
                  <input
                    type="number"
                    defaultValue={45}
                    max={100}
                    min={0}
                    className="flex-1 px-2 py-1 text-sm border border-slate-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                  <span className="text-sm text-slate-500">%</span>
                </div>
                <div className="flex items-center gap-2">
                  <label className="text-sm text-slate-600 w-20">Pro:</label>
                  <input
                    type="number"
                    defaultValue={5}
                    max={100}
                    min={0}
                    className="flex-1 px-2 py-1 text-sm border border-slate-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                  <span className="text-sm text-slate-500">%</span>
                </div>
                <div className="flex items-center gap-2">
                  <label className="text-sm text-slate-600 w-20">Enterprise:</label>
                  <input
                    type="number"
                    defaultValue={1}
                    max={100}
                    min={0}
                    className="flex-1 px-2 py-1 text-sm border border-slate-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                  <span className="text-sm text-slate-500">%</span>
                </div>
              </div>
              <div className="mt-3 p-2 bg-blue-50 rounded text-sm">
                <span className="font-medium text-blue-900">Blended ARPU: $83</span>
              </div>
            </div>
          </div>

          {/* Employee Productivity */}
          <div className="space-y-4">
            <h3 className="font-medium text-slate-900 border-b border-slate-200 pb-2">Employee Productivity</h3>

            <div>
              <h4 className="text-sm font-medium text-slate-700 mb-3">Sales Performance</h4>
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <label className="text-sm text-slate-600 w-24">Customers/rep:</label>
                  <input
                    type="number"
                    defaultValue={6.5}
                    step={0.1}
                    className="flex-1 px-2 py-1 text-sm border border-slate-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                  <span className="text-sm text-slate-500">/mo</span>
                </div>
                <div className="flex items-center gap-2">
                  <label className="text-sm text-slate-600 w-24">Ramp time:</label>
                  <input
                    type="number"
                    defaultValue={2}
                    className="flex-1 px-2 py-1 text-sm border border-slate-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                  <span className="text-sm text-slate-500">mo</span>
                </div>
              </div>
            </div>

            <div>
              <h4 className="text-sm font-medium text-slate-700 mb-3">Marketing Performance</h4>
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <label className="text-sm text-slate-600 w-24">Leads/marketer:</label>
                  <input
                    type="number"
                    defaultValue={200}
                    className="flex-1 px-2 py-1 text-sm border border-slate-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                  <span className="text-sm text-slate-500">/mo</span>
                </div>
                <div className="flex items-center gap-2">
                  <label className="text-sm text-slate-600 w-24">Conversion:</label>
                  <input
                    type="number"
                    defaultValue={12}
                    max={100}
                    min={0}
                    className="flex-1 px-2 py-1 text-sm border border-slate-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                  <span className="text-sm text-slate-500">%</span>
                </div>
              </div>
            </div>
          </div>

          {/* Conversion Metrics */}
          <div className="space-y-4">
            <h3 className="font-medium text-slate-900 border-b border-slate-200 pb-2">Conversion Metrics</h3>
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <label className="text-sm text-slate-600 w-24">Free-to-paid:</label>
                <input
                  type="number"
                  defaultValue={10}
                  max={100}
                  min={0}
                  className="flex-1 px-2 py-1 text-sm border border-slate-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
                <span className="text-sm text-slate-500">%</span>
              </div>
              <div className="flex items-center gap-2">
                <label className="text-sm text-slate-600 w-24">Churn rate:</label>
                <input
                  type="number"
                  defaultValue={2}
                  max={100}
                  min={0}
                  step={0.1}
                  className="flex-1 px-2 py-1 text-sm border border-slate-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
                <span className="text-sm text-slate-500">%/mo</span>
              </div>
            </div>
          </div>

          {/* Salary Benchmarks */}
          <div className="space-y-4">
            <h3 className="font-medium text-slate-900 border-b border-slate-200 pb-2">Salary Benchmarks</h3>
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <label className="text-sm text-slate-600 w-20">Sales Rep:</label>
                <input
                  type="number"
                  defaultValue={3000}
                  className="flex-1 px-2 py-1 text-sm border border-slate-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
                <span className="text-sm text-slate-500">/mo</span>
              </div>
              <div className="flex items-center gap-2">
                <label className="text-sm text-slate-600 w-20">Marketing:</label>
                <input
                  type="number"
                  defaultValue={3000}
                  className="flex-1 px-2 py-1 text-sm border border-slate-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
                <span className="text-sm text-slate-500">/mo</span>
              </div>
              <div className="flex items-center gap-2">
                <label className="text-sm text-slate-600 w-20">Developer:</label>
                <input
                  type="number"
                  defaultValue={4000}
                  className="flex-1 px-2 py-1 text-sm border border-slate-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
                <span className="text-sm text-slate-500">/mo</span>
              </div>
              <div className="flex items-center gap-2">
                <label className="text-sm text-slate-600 w-20">Operations:</label>
                <input
                  type="number"
                  defaultValue={3000}
                  className="flex-1 px-2 py-1 text-sm border border-slate-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
                <span className="text-sm text-slate-500">/mo</span>
              </div>
              <div className="flex items-center gap-2">
                <label className="text-sm text-slate-600 w-20">Leadership:</label>
                <input
                  type="number"
                  defaultValue={5000}
                  className="flex-1 px-2 py-1 text-sm border border-slate-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
                <span className="text-sm text-slate-500">/mo</span>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
