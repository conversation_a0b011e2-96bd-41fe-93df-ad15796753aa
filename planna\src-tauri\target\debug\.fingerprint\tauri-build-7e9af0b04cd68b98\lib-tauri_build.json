{"rustc": 1842507548689473721, "features": "[\"config-json\", \"default\"]", "declared_features": "[\"codegen\", \"config-json\", \"config-json5\", \"config-toml\", \"default\", \"isolation\", \"quote\", \"tauri-codegen\"]", "target": 1006236803848883740, "profile": 2225463790103693989, "path": 12878406917801708813, "deps": [[2671782512663819132, "tauri_utils", false, 9997456220751863589], [4899080583175475170, "semver", false, 1510945248306732154], [6913375703034175521, "schemars", false, 17008654131786635838], [7170110829644101142, "json_patch", false, 11543372679734088708], [9689903380558560274, "serde", false, 14259188998479785018], [12714016054753183456, "tauri_winres", false, 10782623580957958157], [13077543566650298139, "heck", false, 9773368077212947622], [13475171727366188400, "cargo_toml", false, 2117443112297649477], [13625485746686963219, "anyhow", false, 15029994100314801627], [15367738274754116744, "serde_json", false, 5877451486395265118], [15609422047640926750, "toml", false, 10475929893644939239], [15622660310229662834, "walkdir", false, 12728780776187979975], [16928111194414003569, "dirs", false, 15306885009778154891], [17155886227862585100, "glob", false, 16602312495278986287]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri-build-7e9af0b04cd68b98\\dep-lib-tauri_build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}